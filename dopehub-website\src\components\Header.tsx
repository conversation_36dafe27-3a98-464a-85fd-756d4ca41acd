import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import logo from '../assets/logo.PNG';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <header className={`header ${isScrolled ? 'scrolled' : ''}`}>
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <img src={logo} alt="DopeHub Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="desktop-nav">
            <ul className="nav-list">
              <li><button onClick={() => scrollToSection('home')} className="nav-link">Home</button></li>
              <li><button onClick={() => scrollToSection('services')} className="nav-link">Services</button></li>
              <li><button onClick={() => scrollToSection('portfolio')} className="nav-link">Portfolio</button></li>
              <li><button onClick={() => scrollToSection('about')} className="nav-link">About</button></li>
              <li><button onClick={() => scrollToSection('testimonials')} className="nav-link">Testimonials</button></li>
              <li><button onClick={() => scrollToSection('contact')} className="nav-link">Contact</button></li>
            </ul>
          </nav>

          {/* CTA Button */}
          <div className="header-cta">
            <button onClick={() => scrollToSection('contact')} className="btn btn-primary">
              Get Quote
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button className="mobile-menu-btn" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>
          <ul className="mobile-nav-list">
            <li><button onClick={() => scrollToSection('home')} className="mobile-nav-link">Home</button></li>
            <li><button onClick={() => scrollToSection('services')} className="mobile-nav-link">Services</button></li>
            <li><button onClick={() => scrollToSection('portfolio')} className="mobile-nav-link">Portfolio</button></li>
            <li><button onClick={() => scrollToSection('about')} className="mobile-nav-link">About</button></li>
            <li><button onClick={() => scrollToSection('testimonials')} className="mobile-nav-link">Testimonials</button></li>
            <li><button onClick={() => scrollToSection('contact')} className="mobile-nav-link">Contact</button></li>
          </ul>
        </nav>
      </div>

      <style jsx>{`
        .header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;
          border-bottom: 1px solid transparent;
        }

        .header.scrolled {
          background: rgba(255, 255, 255, 0.98);
          border-bottom: 1px solid var(--gray-200);
          box-shadow: var(--shadow-md);
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem 0;
        }

        .logo-image {
          height: 40px;
          width: auto;
          object-fit: contain;
        }

        .desktop-nav {
          display: none;
        }

        .nav-list {
          display: flex;
          list-style: none;
          gap: 2rem;
          margin: 0;
          padding: 0;
        }

        .nav-link {
          background: none;
          border: none;
          color: var(--gray-700);
          font-weight: 500;
          cursor: pointer;
          transition: color 0.3s ease;
          font-size: 1rem;
        }

        .nav-link:hover {
          color: var(--primary-blue);
        }

        .header-cta {
          display: none;
        }

        .mobile-menu-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          border: none;
          color: var(--gray-700);
          cursor: pointer;
          padding: 0.5rem;
        }

        .mobile-nav {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: var(--white);
          border-top: 1px solid var(--gray-200);
          transform: translateY(-100%);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }

        .mobile-nav.open {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }

        .mobile-nav-list {
          list-style: none;
          margin: 0;
          padding: 1rem 0;
        }

        .mobile-nav-link {
          display: block;
          width: 100%;
          background: none;
          border: none;
          color: var(--gray-700);
          font-weight: 500;
          cursor: pointer;
          padding: 1rem;
          text-align: left;
          transition: all 0.3s ease;
          font-size: 1rem;
        }

        .mobile-nav-link:hover {
          background: var(--gray-50);
          color: var(--primary-blue);
        }

        @media (min-width: 768px) {
          .desktop-nav {
            display: block;
          }

          .header-cta {
            display: block;
          }

          .mobile-menu-btn {
            display: none;
          }

          .mobile-nav {
            display: none;
          }
        }
      `}</style>
    </header>
  );
};

export default Header;
