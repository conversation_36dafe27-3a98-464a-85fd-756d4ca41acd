import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';

const Portfolio: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  const portfolioItems = [
    {
      id: 1,
      title: 'Premium DTF T-Shirt Design',
      category: 'dtf',
      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800&h=600&fit=crop',
      description: 'High-quality DTF print featuring vibrant colors and durable finish on premium cotton fabric. Perfect for custom branding and promotional wear.',
      details: 'Material: Premium Cotton | Size: XS-XXL | Colors: Full CMYK'
    },
    {
      id: 2,
      title: 'Corporate Logo Embroidery',
      category: 'embroidery',
      image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=800&h=600&fit=crop',
      description: 'Professional embroidered corporate logo on premium polo shirts. Precision stitching with premium threads for lasting brand impression.',
      details: 'Thread: Premium Polyester | Stitch Count: 8,500 | Colors: 4-Color Design'
    },
    {
      id: 3,
      title: 'Commercial Vehicle Decal',
      category: 'vinyl',
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',
      description: 'Weather-resistant vinyl decal for commercial vehicle branding. UV-resistant materials ensure long-lasting outdoor performance.',
      details: 'Material: 3M Vinyl | Durability: 7+ Years | Finish: Matte/Gloss Options'
    },
    {
      id: 4,
      title: 'Custom Sublimation Mug',
      category: 'sublimation',
      image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=800&h=600&fit=crop',
      description: 'Full-color sublimation print on premium ceramic mug. Dishwasher safe with fade-resistant colors for daily use.',
      details: 'Material: Ceramic | Capacity: 11oz/15oz | Print: 360° Full Wrap'
    },
    {
      id: 5,
      title: 'Event Screen Printing',
      category: 'screen-print',
      image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=800&h=600&fit=crop',
      description: 'Bulk screen printing for corporate events. Consistent quality across large quantities with fast turnaround times.',
      details: 'Quantity: 500+ Units | Inks: Water-based/Plastisol | Colors: Up to 6 Colors'
    },
    {
      id: 6,
      title: 'Complete Brand Identity',
      category: 'design',
      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=600&fit=crop',
      description: 'Comprehensive brand identity package including logo design, business cards, letterheads, and marketing materials.',
      details: 'Package: Logo + Stationery | Formats: Print & Digital | Revisions: Unlimited'
    },
    {
      id: 7,
      title: 'Sports Team DTF Jerseys',
      category: 'dtf',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
      description: 'Custom DTF prints for local sports team jerseys. Durable prints that withstand intense sports activities and frequent washing.',
      details: 'Material: Performance Polyester | Numbers: Individual | Durability: 50+ Washes'
    },
    {
      id: 8,
      title: 'Promotional Embroidered Caps',
      category: 'embroidery',
      image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=800&h=600&fit=crop',
      description: 'High-quality embroidered caps for promotional campaigns. Premium materials with professional finishing for brand representation.',
      details: 'Material: Cotton Twill | Closure: Adjustable | Embroidery: 3D Puff Available'
    },
    {
      id: 9,
      title: 'Storefront Window Graphics',
      category: 'vinyl',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
      description: 'Large format vinyl graphics for storefront windows. Eye-catching designs that attract customers and enhance brand visibility.',
      details: 'Material: Perforated Vinyl | Size: Custom | Installation: Professional'
    }
  ];

  const filters = [
    { key: 'all', label: 'All Work', color: 'primary' },
    { key: 'dtf', label: 'DTF Printing', color: 'blue' },
    { key: 'embroidery', label: 'Embroidery', color: 'orange' },
    { key: 'vinyl', label: 'Vinyl Cutting', color: 'green' },
    { key: 'sublimation', label: 'Sublimation', color: 'purple' },
    { key: 'screen-print', label: 'Screen Print', color: 'indigo' },
    { key: 'design', label: 'Branding', color: 'pink' }
  ];

  const filteredItems = activeFilter === 'all'
    ? portfolioItems
    : portfolioItems.filter(item => item.category === activeFilter);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlay || filteredItems.length === 0) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % filteredItems.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [currentSlide, isAutoPlay, filteredItems.length]);

  // Reset slide when filter changes
  useEffect(() => {
    setCurrentSlide(0);
  }, [activeFilter]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % filteredItems.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + filteredItems.length) % filteredItems.length);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  const currentItem = filteredItems[currentSlide];

  return (
    <section id="portfolio" className="portfolio">
      <div className="container">
        <div className="portfolio-header">
          <h2 className="portfolio-title">Our Portfolio</h2>
          <p className="portfolio-subtitle">
            Explore our diverse range of printing projects and see the quality that sets us apart.
          </p>
        </div>

        {/* Glass-style Filter Navigation */}
        <div className="portfolio-filters">
          {filters.map((filter) => (
            <button
              key={filter.key}
              className={`filter-btn ${activeFilter === filter.key ? 'active' : ''} filter-${filter.color}`}
              onClick={() => setActiveFilter(filter.key)}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Sliding Gallery */}
        <div className="portfolio-gallery">
          <div className="gallery-container">
            {filteredItems.length > 0 && currentItem && (
              <>
                <div className="gallery-slide" key={currentSlide}>
                  {/* Image Section */}
                  <div className="gallery-image-section">
                    <div className="gallery-image">
                      <img src={currentItem.image} alt={currentItem.title} />
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="gallery-content-section">
                    <div className="gallery-content">
                      <h3 className="gallery-title">{currentItem.title}</h3>
                      <p className="gallery-description">{currentItem.description}</p>
                      <div className="gallery-details">
                        <h4>Specifications:</h4>
                        <p>{currentItem.details}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Navigation Controls */}
                <button className="gallery-nav prev" onClick={prevSlide}>
                  <ChevronLeft size={24} />
                </button>
                <button className="gallery-nav next" onClick={nextSlide}>
                  <ChevronRight size={24} />
                </button>

                {/* Auto-play Control */}
                <button className="autoplay-btn" onClick={toggleAutoPlay}>
                  {isAutoPlay ? <Pause size={20} /> : <Play size={20} />}
                </button>

                {/* Slide Indicators */}
                <div className="gallery-indicators">
                  {filteredItems.map((_, index) => (
                    <button
                      key={index}
                      className={`indicator ${index === currentSlide ? 'active' : ''}`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>

                {/* Slide Counter */}
                <div className="slide-counter">
                  {currentSlide + 1} / {filteredItems.length}
                </div>
              </>
            )}
          </div>
        </div>

        <div className="portfolio-cta">
          <p>Have a project in mind?</p>
          <button
            className="btn btn-primary"
            onClick={() => {
              const element = document.getElementById('contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
          >
            Start Your Project
          </button>
        </div>
      </div>

      <style jsx>{`
        .portfolio {
          padding: 5rem 0;
          background: linear-gradient(135deg, rgba(249, 115, 22, 0.02) 0%, var(--white) 30%, rgba(37, 99, 235, 0.025) 100%);
        }

        .portfolio-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .portfolio-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .portfolio-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        /* Glass-style Filter Navigation */
        .portfolio-filters {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          margin-bottom: 4rem;
          flex-wrap: wrap;
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(20px);
          padding: 1rem 2rem;
          border-radius: 2rem;
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          max-width: fit-content;
          margin-left: auto;
          margin-right: auto;
        }

        .filter-btn {
          background: transparent;
          border: none;
          color: var(--gray-700);
          padding: 0.75rem 1.5rem;
          border-radius: 1.5rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.875rem;
          position: relative;
        }

        .filter-btn:hover {
          background: rgba(37, 99, 235, 0.1);
          color: var(--primary-blue);
        }

        .filter-btn.active {
          background: var(--gradient-primary);
          color: var(--white);
          box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        /* Sliding Gallery */
        .portfolio-gallery {
          margin-bottom: 4rem;
        }

        .gallery-container {
          position: relative;
          max-width: 1200px;
          margin: 0 auto;
          border-radius: 2rem;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
          background: linear-gradient(135deg, var(--white) 0%, rgba(37, 99, 235, 0.02) 50%, rgba(249, 115, 22, 0.02) 100%);
          min-height: 500px;
        }

        /* Slide with transition */
        .gallery-slide {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0;
          height: 500px;
          animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        /* Image Section */
        .gallery-image-section {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background: rgba(255, 255, 255, 0.5);
        }

        .gallery-image {
          width: 100%;
          max-width: 400px;
          aspect-ratio: 1;
          border-radius: 1.5rem;
          overflow: hidden;
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
          animation: imageSlideIn 0.8s ease-out 0.2s both;
        }

        @keyframes imageSlideIn {
          from {
            opacity: 0;
            transform: translateX(-40px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        .gallery-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .gallery-image:hover img {
          transform: scale(1.05);
        }

        /* Content Section */
        .gallery-content-section {
          display: flex;
          align-items: center;
          padding: 3rem;
          background: rgba(255, 255, 255, 0.8);
        }

        .gallery-content {
          width: 100%;
          animation: contentSlideIn 0.8s ease-out 0.4s both;
        }

        @keyframes contentSlideIn {
          from {
            opacity: 0;
            transform: translateX(40px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        .gallery-title {
          font-size: 2.2rem;
          font-weight: 800;
          margin-bottom: 1.5rem;
          color: var(--gray-900);
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .gallery-description {
          font-size: 1.125rem;
          line-height: 1.7;
          margin-bottom: 2rem;
          color: var(--gray-700);
        }

        .gallery-details {
          background: rgba(37, 99, 235, 0.05);
          padding: 1.5rem;
          border-radius: 1rem;
          border-left: 4px solid var(--primary-blue);
        }

        .gallery-details h4 {
          font-size: 1rem;
          font-weight: 700;
          color: var(--primary-blue);
          margin-bottom: 0.75rem;
        }

        .gallery-details p {
          font-size: 0.95rem;
          color: var(--gray-600);
          margin: 0;
          line-height: 1.6;
        }

        /* Navigation Controls */
        .gallery-nav {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(15px);
          border: 2px solid rgba(37, 99, 235, 0.1);
          border-radius: 50%;
          width: 4rem;
          height: 4rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--gray-700);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          z-index: 20;
        }

        .gallery-nav:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 12px 35px rgba(37, 99, 235, 0.3);
          border-color: var(--primary-blue);
        }

        .gallery-nav.prev {
          left: 2rem;
        }

        .gallery-nav.next {
          right: 2rem;
        }

        /* Auto-play Control */
        .autoplay-btn {
          position: absolute;
          top: 1.5rem;
          right: 1.5rem;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(10px);
          border: none;
          border-radius: 50%;
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--white);
        }

        .autoplay-btn:hover {
          background: rgba(0, 0, 0, 0.8);
          transform: scale(1.1);
        }

        /* Slide Indicators */
        .gallery-indicators {
          position: absolute;
          bottom: 1.5rem;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: 0.75rem;
        }

        .indicator {
          width: 0.75rem;
          height: 0.75rem;
          border-radius: 50%;
          border: none;
          background: rgba(255, 255, 255, 0.5);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .indicator.active {
          background: var(--white);
          transform: scale(1.3);
        }

        .indicator:hover {
          background: rgba(255, 255, 255, 0.8);
        }

        /* Slide Counter */
        .slide-counter {
          position: absolute;
          top: 1.5rem;
          left: 1.5rem;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(10px);
          color: var(--white);
          padding: 0.5rem 1rem;
          border-radius: 1.5rem;
          font-size: 0.875rem;
          font-weight: 600;
        }

        /* CTA Section */
        .portfolio-cta {
          text-align: center;
          padding: 3rem 2rem;
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(20px);
          border-radius: 2rem;
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .portfolio-cta p {
          font-size: 1.125rem;
          color: var(--gray-700);
          margin-bottom: 1.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .portfolio-title {
            font-size: 2rem;
          }

          .portfolio-filters {
            padding: 0.75rem 1rem;
            gap: 0.5rem;
          }

          .filter-btn {
            font-size: 0.75rem;
            padding: 0.5rem 1rem;
          }

          .gallery-container {
            border-radius: 1rem;
            min-height: auto;
          }

          .gallery-slide {
            grid-template-columns: 1fr;
            height: auto;
          }

          .gallery-image-section {
            padding: 2rem 1.5rem 1rem;
            order: 1;
          }

          .gallery-image {
            max-width: 300px;
            aspect-ratio: 4/3;
          }

          .gallery-content-section {
            padding: 1rem 1.5rem 2rem;
            order: 2;
          }

          .gallery-title {
            font-size: 1.8rem;
          }

          .gallery-description {
            font-size: 1rem;
          }

          .gallery-details {
            padding: 1rem;
          }

          .gallery-nav {
            width: 3rem;
            height: 3rem;
          }

          .gallery-nav.prev {
            left: 1rem;
          }

          .gallery-nav.next {
            right: 1rem;
          }

          .autoplay-btn {
            width: 2.5rem;
            height: 2.5rem;
            top: 1rem;
            right: 1rem;
          }

          .slide-counter {
            top: 1rem;
            left: 1rem;
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
          }

          .gallery-indicators {
            bottom: 1rem;
          }

          .indicator {
            width: 0.6rem;
            height: 0.6rem;
          }
        }
      `}</style>
    </section>
  );
};

export default Portfolio;
