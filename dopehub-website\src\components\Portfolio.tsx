import React, { useState } from 'react';
import { ExternalLink, Filter } from 'lucide-react';

const Portfolio: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const portfolioItems = [
    {
      id: 1,
      title: 'Custom T-Shirt Design',
      category: 'dtf',
      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop',
      description: 'Vibrant DTF print on premium cotton t-shirt'
    },
    {
      id: 2,
      title: 'Corporate Logo Embroidery',
      category: 'embroidery',
      image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=300&fit=crop',
      description: 'Professional embroidered logo on polo shirts'
    },
    {
      id: 3,
      title: 'Vehicle Decal',
      category: 'vinyl',
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop',
      description: 'Weather-resistant vinyl decal for commercial vehicle'
    },
    {
      id: 4,
      title: 'Custom Mug Design',
      category: 'sublimation',
      image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop',
      description: 'Full-color sublimation print on ceramic mug'
    },
    {
      id: 5,
      title: 'Event T-Shirts',
      category: 'screen-print',
      image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=400&h=300&fit=crop',
      description: 'Bulk screen printing for corporate event'
    },
    {
      id: 6,
      title: 'Brand Identity Package',
      category: 'design',
      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop',
      description: 'Complete brand identity and print materials'
    },
    {
      id: 7,
      title: 'Sports Team Jerseys',
      category: 'dtf',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      description: 'Custom DTF prints for local sports team'
    },
    {
      id: 8,
      title: 'Promotional Caps',
      category: 'embroidery',
      image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=400&h=300&fit=crop',
      description: 'Embroidered caps for promotional campaign'
    },
    {
      id: 9,
      title: 'Window Graphics',
      category: 'vinyl',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
      description: 'Large format vinyl graphics for storefront'
    }
  ];

  const filters = [
    { key: 'all', label: 'All Work' },
    { key: 'dtf', label: 'DTF Printing' },
    { key: 'embroidery', label: 'Embroidery' },
    { key: 'vinyl', label: 'Vinyl Cutting' },
    { key: 'sublimation', label: 'Sublimation' },
    { key: 'screen-print', label: 'Screen Print' },
    { key: 'design', label: 'Design' }
  ];

  const filteredItems = activeFilter === 'all' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeFilter);

  return (
    <section id="portfolio" className="portfolio">
      <div className="container">
        <div className="portfolio-header">
          <h2 className="portfolio-title">Our Portfolio</h2>
          <p className="portfolio-subtitle">
            Explore our diverse range of printing projects and see the quality that sets us apart.
          </p>
        </div>

        <div className="portfolio-filters">
          <div className="filter-icon">
            <Filter size={20} />
          </div>
          {filters.map((filter) => (
            <button
              key={filter.key}
              className={`filter-btn ${activeFilter === filter.key ? 'active' : ''}`}
              onClick={() => setActiveFilter(filter.key)}
            >
              {filter.label}
            </button>
          ))}
        </div>

        <div className="portfolio-grid">
          {filteredItems.map((item) => (
            <div key={item.id} className="portfolio-item">
              <div className="portfolio-image">
                <img src={item.image} alt={item.title} />
                <div className="portfolio-overlay">
                  <div className="portfolio-content">
                    <h3 className="portfolio-item-title">{item.title}</h3>
                    <p className="portfolio-item-description">{item.description}</p>
                    <button className="portfolio-link">
                      <ExternalLink size={16} />
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="portfolio-cta">
          <p>Have a project in mind?</p>
          <button
            className="btn btn-primary"
            onClick={() => {
              const element = document.getElementById('contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
          >
            Start Your Project
          </button>
        </div>
      </div>

      <style jsx>{`
        .portfolio {
          padding: 5rem 0;
          background: linear-gradient(135deg, rgba(249, 115, 22, 0.02) 0%, var(--white) 30%, rgba(37, 99, 235, 0.025) 100%);
        }

        .portfolio-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .portfolio-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .portfolio-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        .portfolio-filters {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          margin-bottom: 3rem;
          flex-wrap: wrap;
        }

        .filter-icon {
          color: var(--gray-500);
        }

        .filter-btn {
          background: transparent;
          border: 2px solid var(--gray-300);
          color: var(--gray-700);
          padding: 0.5rem 1rem;
          border-radius: 2rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.875rem;
        }

        .filter-btn:hover {
          border-color: var(--primary-blue);
          color: var(--primary-blue);
        }

        .filter-btn.active {
          background: var(--gradient-primary);
          border-color: transparent;
          color: var(--white);
        }

        .portfolio-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-bottom: 4rem;
        }

        .portfolio-item {
          border-radius: 1rem;
          overflow: hidden;
          box-shadow: var(--shadow-md);
          transition: all 0.3s ease;
        }

        .portfolio-item:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
        }

        .portfolio-image {
          position: relative;
          aspect-ratio: 4/3;
          overflow: hidden;
        }

        .portfolio-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .portfolio-item:hover .portfolio-image img {
          transform: scale(1.05);
        }

        .portfolio-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.7) 100%
          );
          display: flex;
          align-items: flex-end;
          padding: 1.5rem;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .portfolio-item:hover .portfolio-overlay {
          opacity: 1;
        }

        .portfolio-content {
          color: var(--white);
        }

        .portfolio-item-title {
          font-size: 1.25rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }

        .portfolio-item-description {
          font-size: 0.875rem;
          margin-bottom: 1rem;
          opacity: 0.9;
        }

        .portfolio-link {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: var(--white);
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }

        .portfolio-link:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
        }

        .portfolio-cta {
          text-align: center;
          padding: 3rem 2rem;
          background: var(--gray-50);
          border-radius: 1rem;
        }

        .portfolio-cta p {
          font-size: 1.125rem;
          color: var(--gray-700);
          margin-bottom: 1.5rem;
        }

        @media (max-width: 768px) {
          .portfolio-title {
            font-size: 2rem;
          }

          .portfolio-filters {
            gap: 0.5rem;
          }

          .filter-btn {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
          }

          .portfolio-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </section>
  );
};

export default Portfolio;
