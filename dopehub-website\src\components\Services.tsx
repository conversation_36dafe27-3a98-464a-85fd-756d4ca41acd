import React from 'react';
import { Shirt, Scissors, Palette, Printer, Zap, Star } from 'lucide-react';

const Services: React.FC = () => {
  const services = [
    {
      icon: <Shirt size={32} />,
      title: 'DTF Printing',
      description: 'Premium Direct-to-Film printing with low MOQ requirements. Perfect for startups and established brands looking for high-quality, vibrant prints.',
      features: ['Low MOQ available', 'Vibrant colors', 'Durable finish', 'Expert quality'],
      color: 'blue'
    },
    {
      icon: <Scissors size={32} />,
      title: 'Embroidery',
      description: 'Professional embroidery services by our expert design team. Custom logos, text, and designs with premium quality stitching.',
      features: ['Expert design team', 'Custom logos', 'Premium threads', 'Trusted quality'],
      color: 'orange'
    },
    {
      icon: <Palette size={32} />,
      title: 'Vinyl Cutting',
      description: 'Precision vinyl cutting for signs, decals, and custom graphics. Perfect for vehicles, windows, and promotional materials.',
      features: ['Precision cutting', 'Weather resistant', 'Custom shapes', 'Various colors'],
      color: 'green'
    },
    {
      icon: <Printer size={32} />,
      title: 'Sublimation',
      description: 'Sublimation printing for mugs, phone cases, and polyester fabrics. Permanent, fade-resistant prints.',
      features: ['Permanent prints', 'Fade resistant', 'Photo quality', 'Various products'],
      color: 'purple'
    },
    {
      icon: <Zap size={32} />,
      title: 'Screen Printing',
      description: 'Traditional screen printing for bulk orders. Cost-effective solution for large quantities with consistent quality.',
      features: ['Bulk orders', 'Cost effective', 'Consistent quality', 'Various inks'],
      color: 'indigo'
    },
    {
      icon: <Star size={32} />,
      title: 'Branding Solutions',
      description: 'Complete branding packages by our expert design team. From concept to final product, we elevate your brand presence.',
      features: ['Brand identity', 'Logo design', 'Marketing materials', 'Complete packages'],
      color: 'pink'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'service-blue',
      orange: 'service-orange',
      green: 'service-green',
      purple: 'service-purple',
      indigo: 'service-indigo',
      pink: 'service-pink'
    };
    return colorMap[color as keyof typeof colorMap] || 'service-blue';
  };

  return (
    <section id="services" className="services">
      <div className="container">
        <div className="services-header">
          <h2 className="services-title">Our Services</h2>
          <p className="services-subtitle">
            Comprehensive printing solutions for all your creative needs. 
            From concept to completion, we deliver excellence in every project.
          </p>
        </div>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className={`service-card ${getColorClasses(service.color)}`}>
              <div className="service-icon">
                {service.icon}
              </div>
              <h3 className="service-title">{service.title}</h3>
              <p className="service-description">{service.description}</p>
              <ul className="service-features">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="service-feature">
                    <span className="feature-bullet">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <button className="service-cta">Learn More</button>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .services {
          padding: 5rem 0;
          background: var(--gray-50);
        }

        .services-header {
          text-align: center;
          margin-bottom: 4rem;
        }

        .services-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .services-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        .services-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
        }

        .service-card {
          background: var(--white);
          border-radius: 1rem;
          padding: 2rem;
          box-shadow: var(--shadow-md);
          border: 1px solid var(--gray-200);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .service-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: var(--gradient-primary);
          transition: height 0.3s ease;
        }

        .service-card:hover {
          transform: translateY(-4px);
          box-shadow: var(--shadow-lg);
        }

        .service-card:hover::before {
          height: 6px;
        }

        .service-icon {
          width: 4rem;
          height: 4rem;
          border-radius: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1.5rem;
          color: var(--white);
        }

        .service-blue .service-icon {
          background: var(--primary-blue);
        }

        .service-orange .service-icon {
          background: var(--primary-orange);
        }

        .service-green .service-icon {
          background: #10b981;
        }

        .service-purple .service-icon {
          background: #8b5cf6;
        }

        .service-indigo .service-icon {
          background: #6366f1;
        }

        .service-pink .service-icon {
          background: #ec4899;
        }

        .service-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .service-description {
          color: var(--gray-600);
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }

        .service-features {
          list-style: none;
          margin: 0 0 2rem 0;
          padding: 0;
        }

        .service-feature {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--gray-700);
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
        }

        .feature-bullet {
          color: var(--primary-blue);
          font-weight: bold;
        }

        .service-cta {
          background: transparent;
          border: 2px solid var(--gray-300);
          color: var(--gray-700);
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 100%;
        }

        .service-blue .service-cta:hover {
          border-color: var(--primary-blue);
          color: var(--primary-blue);
        }

        .service-orange .service-cta:hover {
          border-color: var(--primary-orange);
          color: var(--primary-orange);
        }

        .service-green .service-cta:hover {
          border-color: #10b981;
          color: #10b981;
        }

        .service-purple .service-cta:hover {
          border-color: #8b5cf6;
          color: #8b5cf6;
        }

        .service-indigo .service-cta:hover {
          border-color: #6366f1;
          color: #6366f1;
        }

        .service-pink .service-cta:hover {
          border-color: #ec4899;
          color: #ec4899;
        }

        @media (max-width: 768px) {
          .services-grid {
            grid-template-columns: 1fr;
          }

          .services-title {
            font-size: 2rem;
          }
        }
      `}</style>
    </section>
  );
};

export default Services;
