import React from 'react';
import { Award, Users, Clock, Target, CheckCircle } from 'lucide-react';

const About: React.FC = () => {
  const features = [
    {
      icon: <Award size={24} />,
      title: 'Premium Quality',
      description: 'We use only the finest materials and cutting-edge technology to ensure exceptional results.'
    },
    {
      icon: <Clock size={24} />,
      title: 'Fast Turnaround',
      description: 'Quick delivery without compromising quality. Most orders completed within 24-48 hours.'
    },
    {
      icon: <Users size={24} />,
      title: 'Expert Team',
      description: 'Our skilled professionals bring years of experience in printing and design.'
    },
    {
      icon: <Target size={24} />,
      title: 'Custom Solutions',
      description: 'Tailored printing solutions to meet your specific needs and requirements.'
    }
  ];

  const achievements = [
    { number: '500+', label: 'Projects Completed' },
    { number: '100+', label: 'Happy Clients' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Customer Support' }
  ];

  const values = [
    'Quality craftsmanship in every project',
    'Customer satisfaction is our priority',
    'Innovation in printing technology',
    'Sustainable and eco-friendly practices',
    'Competitive pricing with no hidden costs',
    'Professional consultation and support'
  ];

  return (
    <section id="about" className="about">
      <div className="container">
        <div className="about-content">
          <div className="about-text">
            <h2 className="about-title">About DopeHub</h2>
            <p className="about-description">
              At DopeHub, we're passionate about transforming your creative visions into stunning reality. 
              With years of experience in the printing industry, we've built our reputation on delivering 
              exceptional quality, innovative solutions, and outstanding customer service.
            </p>
            <p className="about-description">
              From small personal projects to large corporate campaigns, we handle every job with the same 
              level of dedication and attention to detail. Our state-of-the-art equipment and skilled team 
              ensure that your prints not only meet but exceed your expectations.
            </p>

            <div className="about-values">
              <h3 className="values-title">Why Choose Us?</h3>
              <ul className="values-list">
                {values.map((value, index) => (
                  <li key={index} className="value-item">
                    <CheckCircle size={16} />
                    <span>{value}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="about-visual">
            <div className="about-image">
              <img 
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=600&fit=crop" 
                alt="DopeHub printing facility" 
              />
              <div className="image-overlay">
                <div className="overlay-content">
                  <h4>State-of-the-art Facility</h4>
                  <p>Modern equipment for superior results</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="about-features">
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">
                  {feature.icon}
                </div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="about-achievements">
          <div className="achievements-grid">
            {achievements.map((achievement, index) => (
              <div key={index} className="achievement-item">
                <div className="achievement-number">{achievement.number}</div>
                <div className="achievement-label">{achievement.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        .about {
          padding: 5rem 0;
          background: var(--gray-50);
        }

        .about-content {
          display: grid;
          grid-template-columns: 1fr;
          gap: 3rem;
          align-items: center;
          margin-bottom: 4rem;
        }

        .about-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1.5rem;
        }

        .about-description {
          font-size: 1.125rem;
          line-height: 1.7;
          color: var(--gray-600);
          margin-bottom: 1.5rem;
        }

        .about-values {
          margin-top: 2rem;
        }

        .values-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .values-list {
          list-style: none;
          margin: 0;
          padding: 0;
        }

        .value-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 0.75rem;
          color: var(--gray-700);
        }

        .value-item svg {
          color: var(--primary-blue);
          flex-shrink: 0;
        }

        .about-visual {
          display: flex;
          justify-content: center;
        }

        .about-image {
          position: relative;
          border-radius: 1rem;
          overflow: hidden;
          box-shadow: var(--shadow-lg);
          max-width: 400px;
        }

        .about-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .image-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0) 100%
          );
          padding: 2rem;
          color: var(--white);
        }

        .overlay-content h4 {
          font-size: 1.25rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }

        .overlay-content p {
          font-size: 0.875rem;
          opacity: 0.9;
        }

        .about-features {
          margin-bottom: 4rem;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .feature-card {
          background: var(--white);
          padding: 2rem;
          border-radius: 1rem;
          box-shadow: var(--shadow-md);
          text-align: center;
          transition: transform 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-4px);
        }

        .feature-icon {
          width: 3rem;
          height: 3rem;
          background: var(--gradient-primary);
          border-radius: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1rem;
          color: var(--white);
        }

        .feature-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 0.75rem;
        }

        .feature-description {
          color: var(--gray-600);
          line-height: 1.6;
        }

        .about-achievements {
          background: var(--white);
          padding: 3rem 2rem;
          border-radius: 1rem;
          box-shadow: var(--shadow-md);
        }

        .achievements-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 2rem;
        }

        .achievement-item {
          text-align: center;
        }

        .achievement-number {
          font-size: 3rem;
          font-weight: 800;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          margin-bottom: 0.5rem;
        }

        .achievement-label {
          font-size: 1rem;
          color: var(--gray-600);
          font-weight: 500;
        }

        @media (min-width: 768px) {
          .about-content {
            grid-template-columns: 1fr 1fr;
          }
        }

        @media (max-width: 768px) {
          .about-title {
            font-size: 2rem;
          }

          .achievements-grid {
            grid-template-columns: repeat(2, 1fr);
          }

          .achievement-number {
            font-size: 2rem;
          }
        }
      `}</style>
    </section>
  );
};

export default About;
