import React from 'react';
import { ArrowRight, Sparkles } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToPortfolio = () => {
    const element = document.getElementById('portfolio');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="hero">
      <div className="hero-background">
        <div className="hero-gradient"></div>
        <div className="hero-pattern"></div>
      </div>
      
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <div className="hero-badge">
              <Sparkles size={16} />
              <span>Premium Printing Solutions</span>
            </div>
            
            <h1 className="hero-title">
              Transform Your Ideas Into
              <span className="gradient-text"> Stunning Prints</span>
            </h1>
            
            <p className="hero-description">
              From DTF transfers to custom embroidery, we bring your creative visions to life with 
              cutting-edge printing technology and unmatched quality. Your one-stop destination 
              for all printing needs.
            </p>
            
            <div className="hero-buttons">
              <button onClick={scrollToContact} className="btn btn-primary">
                Get Started
                <ArrowRight size={20} />
              </button>
              <button onClick={scrollToPortfolio} className="btn btn-secondary">
                View Portfolio
              </button>
            </div>
            
            <div className="hero-stats">
              <div className="stat">
                <div className="stat-number">500+</div>
                <div className="stat-label">Projects Completed</div>
              </div>
              <div className="stat">
                <div className="stat-number">100+</div>
                <div className="stat-label">Happy Clients</div>
              </div>
              <div className="stat">
                <div className="stat-number">24/7</div>
                <div className="stat-label">Support</div>
              </div>
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="hero-card">
              <div className="card-glow"></div>
              <div className="card-content">
                <div className="service-preview">
                  <div className="preview-item dtf">
                    <div className="preview-icon">DTF</div>
                    <div className="preview-text">Direct to Film</div>
                  </div>
                  <div className="preview-item embroidery">
                    <div className="preview-icon">EMB</div>
                    <div className="preview-text">Embroidery</div>
                  </div>
                  <div className="preview-item vinyl">
                    <div className="preview-icon">VIN</div>
                    <div className="preview-text">Vinyl Cutting</div>
                  </div>
                  <div className="preview-item sublimation">
                    <div className="preview-icon">SUB</div>
                    <div className="preview-text">Sublimation</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .hero {
          position: relative;
          min-height: 100vh;
          display: flex;
          align-items: center;
          padding-top: 80px;
          overflow: hidden;
        }

        .hero-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: -1;
        }

        .hero-gradient {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, 
            rgba(37, 99, 235, 0.05) 0%, 
            rgba(249, 115, 22, 0.05) 100%);
        }

        .hero-pattern {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: 
            radial-gradient(circle at 25% 25%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(249, 115, 22, 0.1) 0%, transparent 50%);
        }

        .hero-content {
          display: grid;
          grid-template-columns: 1fr;
          gap: 3rem;
          align-items: center;
        }

        .hero-badge {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          background: rgba(37, 99, 235, 0.1);
          color: var(--primary-blue);
          padding: 0.5rem 1rem;
          border-radius: 2rem;
          font-size: 0.875rem;
          font-weight: 500;
          margin-bottom: 1.5rem;
        }

        .hero-title {
          font-size: 3rem;
          font-weight: 800;
          line-height: 1.1;
          margin-bottom: 1.5rem;
          color: var(--gray-900);
        }

        .gradient-text {
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .hero-description {
          font-size: 1.125rem;
          line-height: 1.7;
          color: var(--gray-600);
          margin-bottom: 2rem;
          max-width: 600px;
        }

        .hero-buttons {
          display: flex;
          gap: 1rem;
          margin-bottom: 3rem;
          flex-wrap: wrap;
        }

        .hero-stats {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          max-width: 400px;
        }

        .stat {
          text-align: center;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 800;
          color: var(--primary-blue);
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.875rem;
          color: var(--gray-600);
          font-weight: 500;
        }

        .hero-visual {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .hero-card {
          position: relative;
          background: var(--white);
          border-radius: 1.5rem;
          padding: 2rem;
          box-shadow: var(--shadow-xl);
          border: 1px solid var(--gray-200);
        }

        .card-glow {
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: var(--gradient-primary);
          border-radius: 1.5rem;
          z-index: -1;
          opacity: 0.1;
        }

        .service-preview {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 1.5rem;
        }

        .preview-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          border-radius: 0.75rem;
          transition: transform 0.3s ease;
        }

        .preview-item:hover {
          transform: translateY(-2px);
        }

        .preview-item.dtf {
          background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.1));
        }

        .preview-item.embroidery {
          background: linear-gradient(135deg, rgba(249, 115, 22, 0.1), rgba(251, 146, 60, 0.1));
        }

        .preview-item.vinyl {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
        }

        .preview-item.sublimation {
          background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(167, 139, 250, 0.1));
        }

        .preview-icon {
          width: 3rem;
          height: 3rem;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          font-size: 0.875rem;
          color: var(--white);
        }

        .dtf .preview-icon {
          background: var(--primary-blue);
        }

        .embroidery .preview-icon {
          background: var(--primary-orange);
        }

        .vinyl .preview-icon {
          background: #10b981;
        }

        .sublimation .preview-icon {
          background: #8b5cf6;
        }

        .preview-text {
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--gray-700);
        }

        @media (min-width: 768px) {
          .hero-content {
            grid-template-columns: 1fr 1fr;
          }

          .hero-title {
            font-size: 4rem;
          }
        }

        @media (max-width: 640px) {
          .hero-title {
            font-size: 2.5rem;
          }

          .hero-buttons {
            flex-direction: column;
          }

          .hero-stats {
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
          }

          .stat-number {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </section>
  );
};

export default Hero;
