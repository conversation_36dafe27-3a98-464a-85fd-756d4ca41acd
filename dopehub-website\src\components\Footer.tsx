import React from 'react';
import { MessageCircle, Phone, Mail, MapPin, Instagram, Facebook, Twitter, ArrowUp } from 'lucide-react';
import logo from '../assets/logo.PNG';

const Footer: React.FC = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const openWhatsApp = () => {
    const whatsappNumber = '2347017775411';
    const message = encodeURIComponent('Hi DopeHub! I\'m interested in your printing services. Please provide me with more information.');
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const services = [
    'DTF Printing',
    'Embroidery',
    '<PERSON>yl Cutting',
    'Sublimation',
    'Screen Printing',
    'Custom Design'
  ];

  const quickLinks = [
    { name: 'Home', id: 'home' },
    { name: 'Services', id: 'services' },
    { name: 'Portfolio', id: 'portfolio' },
    { name: 'About', id: 'about' },
    { name: 'Testimonials', id: 'testimonials' },
    { name: 'Contact', id: 'contact' }
  ];

  const socialLinks = [
    { icon: <Instagram size={20} />, url: 'https://instagram.com/dopehubcreativesolutions', name: 'Instagram' },
    { icon: <Facebook size={20} />, url: 'https://facebook.com/dopehubcreativesolutions', name: 'Facebook' },
    { icon: <Twitter size={20} />, url: 'https://twitter.com/dopehubcreative', name: 'Twitter' }
  ];

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-logo">
              <img src={logo} alt="DopeHub Logo" className="footer-logo-image" />
              <p className="logo-tagline">Your Trusted Print & Brand Slot</p>
            </div>
            <p className="footer-description">
              RC 8020924 - Elevate your brand with DTF, Embroidery, and premium branding solutions.
              Low MOQ, high quality, and trusted by businesses across Lagos, Nigeria.
            </p>
            <div className="social-links">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          <div className="footer-section">
            <h4 className="section-title">Services</h4>
            <ul className="footer-links">
              {services.map((service, index) => (
                <li key={index}>
                  <button 
                    onClick={() => scrollToSection('services')}
                    className="footer-link"
                  >
                    {service}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          <div className="footer-section">
            <h4 className="section-title">Quick Links</h4>
            <ul className="footer-links">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <button 
                    onClick={() => scrollToSection(link.id)}
                    className="footer-link"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          <div className="footer-section">
            <h4 className="section-title">Contact Info</h4>
            <div className="contact-info">
              <div className="contact-item">
                <Phone size={16} />
                <span>+234 ************</span>
              </div>
              <div className="contact-item">
                <Mail size={16} />
                <span><EMAIL></span>
              </div>
              <div className="contact-item">
                <MapPin size={16} />
                <span>HFP Eastline Shopping Complex, Lekki, Lagos</span>
              </div>
              <div className="contact-item">
                <MessageCircle size={16} />
                <span>WhatsApp Orders Available</span>
              </div>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p className="copyright">
              © 2024 DopeHub (RC 8020924). All rights reserved. | Your Trusted Print & Brand Slot in Lagos, Nigeria.
            </p>
            <div className="footer-bottom-links">
              <a href="#" className="bottom-link">Privacy Policy</a>
              <a href="#" className="bottom-link">Terms of Service</a>
              <a href="#" className="bottom-link">Cookie Policy</a>
            </div>
          </div>
        </div>
      </div>

      <button className="scroll-to-top" onClick={scrollToTop} aria-label="Scroll to top">
        <ArrowUp size={20} />
      </button>

      <button className="whatsapp-float" onClick={openWhatsApp} aria-label="Contact us on WhatsApp">
        <MessageCircle size={24} />
      </button>

      <style jsx>{`
        .footer {
          background: var(--gray-900);
          color: var(--white);
          position: relative;
        }

        .footer-content {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 3rem;
          padding: 4rem 0 2rem;
        }

        .footer-section {
          display: flex;
          flex-direction: column;
        }

        .footer-logo {
          margin-bottom: 1rem;
        }

        .footer-logo-image {
          height: 50px;
          width: auto;
          object-fit: contain;
          margin-bottom: 0.5rem;
        }

        .logo-tagline {
          color: var(--gray-400);
          font-size: 0.875rem;
          margin: 0;
        }

        .footer-description {
          color: var(--gray-300);
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }

        .social-links {
          display: flex;
          gap: 1rem;
        }

        .social-link {
          width: 2.5rem;
          height: 2.5rem;
          background: var(--gray-800);
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--gray-300);
          text-decoration: none;
          transition: all 0.3s ease;
        }

        .social-link:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: translateY(-2px);
        }

        .section-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--white);
          margin-bottom: 1.5rem;
        }

        .footer-links {
          list-style: none;
          margin: 0;
          padding: 0;
        }

        .footer-links li {
          margin-bottom: 0.75rem;
        }

        .footer-link {
          background: none;
          border: none;
          color: var(--gray-300);
          cursor: pointer;
          transition: color 0.3s ease;
          font-size: 0.875rem;
          text-align: left;
          padding: 0;
        }

        .footer-link:hover {
          color: var(--primary-blue);
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .contact-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          color: var(--gray-300);
          font-size: 0.875rem;
        }

        .contact-item svg {
          color: var(--primary-blue);
          flex-shrink: 0;
        }

        .footer-bottom {
          border-top: 1px solid var(--gray-800);
          padding: 2rem 0;
        }

        .footer-bottom-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .copyright {
          color: var(--gray-400);
          font-size: 0.875rem;
          margin: 0;
        }

        .footer-bottom-links {
          display: flex;
          gap: 2rem;
        }

        .bottom-link {
          color: var(--gray-400);
          text-decoration: none;
          font-size: 0.875rem;
          transition: color 0.3s ease;
        }

        .bottom-link:hover {
          color: var(--primary-blue);
        }

        .scroll-to-top {
          position: fixed;
          bottom: 2rem;
          right: 2rem;
          width: 3rem;
          height: 3rem;
          background: var(--gradient-primary);
          border: none;
          border-radius: 50%;
          color: var(--white);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: var(--shadow-lg);
          transition: all 0.3s ease;
          z-index: 1000;
        }

        .scroll-to-top:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-xl);
        }

        .whatsapp-float {
          position: fixed;
          bottom: 2rem;
          left: 2rem;
          width: 3.5rem;
          height: 3.5rem;
          background: #25d366;
          border: none;
          border-radius: 50%;
          color: var(--white);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: var(--shadow-lg);
          transition: all 0.3s ease;
          z-index: 1000;
          animation: whatsapp-pulse 2s infinite;
        }

        .whatsapp-float:hover {
          transform: translateY(-2px) scale(1.1);
          box-shadow: var(--shadow-xl);
          background: #128c7e;
        }

        @keyframes whatsapp-pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
          }
        }

        @media (max-width: 768px) {
          .footer-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            padding: 3rem 0 2rem;
          }

          .footer-bottom-content {
            flex-direction: column;
            text-align: center;
          }

          .footer-bottom-links {
            justify-content: center;
          }

          .scroll-to-top {
            bottom: 1rem;
            right: 1rem;
          }

          .whatsapp-float {
            bottom: 1rem;
            left: 1rem;
            width: 3rem;
            height: 3rem;
          }
        }
      `}</style>
    </footer>
  );
};

export default Footer;
