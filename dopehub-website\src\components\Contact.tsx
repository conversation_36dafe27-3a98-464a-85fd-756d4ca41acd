import React, { useState } from 'react';
import { MessageCircle, Phone, Mail, MapPin, Clock, Send } from 'lucide-react';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });
  const [isFormExpanded, setIsFormExpanded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleWhatsAppOrder = () => {
    const { name, phone, service, message } = formData;
    const whatsappMessage = `Hi DopeHub!

I'm interested in your printing services.

Name: ${name || 'Not provided'}
Phone: ${phone || 'Not provided'}
Service: ${service || 'General inquiry'}
Message: ${message || 'I would like to know more about your services.'}

Please provide me with more information and pricing.`;

    const encodedMessage = encodeURIComponent(whatsappMessage);
    const whatsappNumber = '2347017775411';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;

    window.open(whatsappUrl, '_blank');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Show custom success message
    setShowSuccessMessage(true);

    // Reset form and close after showing message
    setFormData({
      name: '',
      email: '',
      phone: '',
      service: '',
      message: ''
    });
    setIsSubmitting(false);
    setIsFormExpanded(false);

    // Hide success message after 4 seconds
    setTimeout(() => {
      setShowSuccessMessage(false);
    }, 4000);
  };

  const toggleForm = () => {
    setIsFormExpanded(!isFormExpanded);
  };

  const contactInfo = [
    {
      icon: <Phone size={24} />,
      title: 'Phone',
      details: ['+234 ************', 'WhatsApp Available'],
      action: 'tel:+2347017775411'
    },
    {
      icon: <Mail size={24} />,
      title: 'Email',
      details: ['<EMAIL>', '<EMAIL>'],
      action: 'mailto:<EMAIL>'
    },
    {
      icon: <MapPin size={24} />,
      title: 'Location',
      details: ['HFP Eastline Shopping Complex', 'Abraham Adesanya, Aja, Lekki 106105, Lagos, Nigeria'],
      action: 'https://maps.google.com/search/HFP+Eastline+shopping+complex+Abraham+Adesanya+Aja+Lekki'
    },
    {
      icon: <Clock size={24} />,
      title: 'Hours',
      details: ['Mon - Fri: 9:00 AM - 6:00 PM', 'Sat: 10:00 AM - 4:00 PM'],
      action: null
    }
  ];

  const services = [
    'DTF Printing',
    'Embroidery',
    'Vinyl Cutting',
    'Sublimation',
    'Screen Printing',
    'Custom Design',
    'Other'
  ];

  return (
    <section id="contact" className="contact">
      <div className="container">
        <div className="contact-header">
          <h2 className="contact-title">Get In Touch</h2>
          <p className="contact-subtitle">
            Ready to elevate your brand? Visit us at HFP Eastline Shopping Complex or contact us for a free quote. Low MOQ, high quality guaranteed!
          </p>
        </div>

        <div className="contact-content">
          <div className="contact-info">
            <h3 className="info-title">Contact Information</h3>
            <p className="info-description">
              Reach out to us through any of these channels. We're here to help with all your printing needs.
            </p>

            <div className="contact-methods">
              {contactInfo.map((info, index) => (
                <div key={index} className="contact-method">
                  <div className="method-icon">
                    {info.icon}
                  </div>
                  <div className="method-details">
                    <h4 className="method-title">{info.title}</h4>
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="method-detail">
                        {info.action && detailIndex === 0 ? (
                          <a href={info.action} target="_blank" rel="noopener noreferrer">
                            {detail}
                          </a>
                        ) : (
                          detail
                        )}
                      </p>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="whatsapp-cta">
              <div className="whatsapp-icon">
                <MessageCircle size={24} />
              </div>
              <div className="whatsapp-text">
                <h4>Quick WhatsApp Order</h4>
                <p>Get instant quotes and place orders directly through WhatsApp</p>
              </div>
            </div>
          </div>

          <div className={`contact-form ${isFormExpanded ? 'expanded' : ''}`}>
            {!isFormExpanded ? (
              <div className="form-button-container">
                <button onClick={toggleForm} className="expand-form-btn">
                  <Send size={24} />
                  <span>Send us a Message</span>
                </button>
              </div>
            ) : (
              <div className="expanded-form">
                <div className="form-header">
                  <h3 className="form-title">Send us a Message</h3>
                  <button onClick={toggleForm} className="close-form-btn">
                    ×
                  </button>
                </div>
                <form className="form" onSubmit={handleSubmit}>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="name" className="form-label">Name *</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="form-input"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="email" className="form-label">Email *</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="form-input"
                        required
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="phone" className="form-label">Phone</label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="form-input"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="service" className="form-label">Service</label>
                      <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Select a service</option>
                        {services.map((service, index) => (
                          <option key={index} value={service}>{service}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="message" className="form-label">Message *</label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={5}
                      className="form-input form-textarea"
                      placeholder="Tell us about your project..."
                      required
                    ></textarea>
                  </div>

                  <div className="form-buttons">
                    <button type="button" onClick={handleWhatsAppOrder} className="btn btn-primary whatsapp-btn">
                      <MessageCircle size={20} />
                      Order via WhatsApp
                    </button>
                    <button type="submit" className="btn btn-secondary" disabled={isSubmitting}>
                      <Send size={20} />
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="success-message">
            <div className="success-content">
              <div className="success-icon">🎉</div>
              <h3>Message Sent Successfully!</h3>
              <p>We'll get back to you soon. Thank you for choosing DopeHub!</p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .contact {
          padding: 5rem 0;
          background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, var(--white) 30%, rgba(249, 115, 22, 0.025) 70%, var(--gray-50) 100%);
        }

        .contact-header {
          text-align: center;
          margin-bottom: 4rem;
        }

        .contact-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .contact-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        .contact-content {
          display: grid;
          grid-template-columns: 1fr;
          gap: 3rem;
        }

        .info-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .info-description {
          color: var(--gray-600);
          margin-bottom: 2rem;
          line-height: 1.6;
        }

        .contact-methods {
          margin-bottom: 2rem;
        }

        .contact-method {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .method-icon {
          width: 3rem;
          height: 3rem;
          background: var(--gradient-primary);
          border-radius: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          flex-shrink: 0;
        }

        .method-title {
          font-weight: 600;
          color: var(--gray-900);
          margin-bottom: 0.25rem;
        }

        .method-detail {
          color: var(--gray-600);
          font-size: 0.875rem;
          margin-bottom: 0.25rem;
        }

        .method-detail a {
          color: var(--primary-blue);
          text-decoration: none;
        }

        .method-detail a:hover {
          text-decoration: underline;
        }

        .whatsapp-cta {
          display: flex;
          align-items: center;
          gap: 1rem;
          background: var(--white);
          padding: 1.5rem;
          border-radius: 1rem;
          box-shadow: var(--shadow-md);
          border: 2px solid #25d366;
        }

        .whatsapp-icon {
          width: 3rem;
          height: 3rem;
          background: #25d366;
          border-radius: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
        }

        .whatsapp-text h4 {
          color: var(--gray-900);
          margin-bottom: 0.25rem;
        }

        .whatsapp-text p {
          color: var(--gray-600);
          font-size: 0.875rem;
        }

        .contact-form {
          background: var(--white);
          border-radius: 2rem;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
          overflow: hidden;
          transition: all 0.5s ease;
          width: fit-content;
          height: fit-content;
          margin: 0 auto;
        }

        .contact-form.expanded {
          width: 100%;
          max-width: 600px;
          height: auto;
        }

        /* Expandable Form Button */
        .form-button-container {
          padding: 2rem;
          text-align: center;
        }

        .expand-form-btn {
          display: inline-flex;
          align-items: center;
          gap: 1rem;
          background: var(--gradient-primary);
          color: var(--white);
          border: none;
          padding: 1.5rem 3rem;
          border-radius: 2rem;
          font-size: 1.125rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .expand-form-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
        }

        /* Expanded Form */
        .expanded-form {
          animation: expandForm 0.5s ease-out;
          padding: 2rem;
        }

        @keyframes expandForm {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .form-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .close-form-btn {
          background: none;
          border: none;
          font-size: 2rem;
          color: var(--gray-500);
          cursor: pointer;
          transition: color 0.3s ease;
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }

        .close-form-btn:hover {
          color: var(--gray-700);
          background: var(--gray-100);
        }

        /* Success Message */
        .success-message {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 1000;
          animation: successSlideIn 0.5s ease-out;
        }

        @keyframes successSlideIn {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }

        .success-content {
          background: var(--white);
          border-radius: 2rem;
          padding: 3rem 2rem;
          text-align: center;
          box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
          border: 2px solid var(--primary-blue);
          max-width: 400px;
        }

        .success-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
        }

        .success-content h3 {
          color: var(--primary-blue);
          font-size: 1.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
        }

        .success-content p {
          color: var(--gray-600);
          font-size: 1rem;
          line-height: 1.6;
          margin: 0;
        }

        .form-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 1.5rem;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1rem;
          margin-bottom: 1rem;
        }

        .form-group {
          margin-bottom: 1rem;
        }

        .form-label {
          display: block;
          font-weight: 500;
          color: var(--gray-700);
          margin-bottom: 0.5rem;
        }

        .form-input {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid var(--gray-300);
          border-radius: 0.5rem;
          font-size: 1rem;
          transition: border-color 0.3s ease;
          font-family: inherit;
        }

        .form-input:focus {
          outline: none;
          border-color: var(--primary-blue);
        }

        .form-textarea {
          resize: vertical;
          min-height: 120px;
        }

        .form-buttons {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .whatsapp-btn {
          background: #25d366 !important;
          border-color: #25d366 !important;
        }

        .whatsapp-btn:hover {
          background: #128c7e !important;
          border-color: #128c7e !important;
        }

        @media (min-width: 768px) {
          .contact-content {
            grid-template-columns: 1fr 1fr;
          }

          .form-row {
            grid-template-columns: 1fr 1fr;
          }
        }

        @media (max-width: 768px) {
          .contact-title {
            font-size: 2rem;
          }

          .form-buttons {
            flex-direction: column;
          }
        }
      `}</style>
    </section>
  );
};

export default Contact;
