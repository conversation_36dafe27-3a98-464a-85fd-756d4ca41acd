import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials = [
    {
      id: 1,
      name: 'Adebayo Ogundimu',
      company: 'Lagos Fashion Brand',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'DopeHub transformed our brand! Their DTF printing quality is exceptional and the low MOQ made it perfect for our startup. Highly recommended!'
    },
    {
      id: 2,
      name: 'Chioma Nwankwo',
      company: 'Event Planning Lagos',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Professional embroidery work for our corporate events. The team at Abraham Adesanya location delivered beyond expectations. Fast and reliable!'
    },
    {
      id: 3,
      name: '<PERSON>eka Okafor',
      company: 'Tech Startup Lekki',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Their expert design team created amazing branded materials for our company. The quality and attention to detail is outstanding!'
    },
    {
      id: 4,
      name: 'Fatima Abdullahi',
      company: 'Restaurant Owner',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Perfect sublimation work on our branded mugs and merchandise. DopeHub is truly the trusted print slot in Lagos!'
    },
    {
      id: 5,
      name: 'Tunde Bakare',
      company: 'Sports Club Lagos',
      image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Handled our bulk jersey order with ease. High quality printing, competitive prices, and excellent customer service. Will definitely return!'
    }
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentIndex, isAutoPlaying]);

  const handleMouseEnter = () => setIsAutoPlaying(false);
  const handleMouseLeave = () => setIsAutoPlaying(true);

  return (
    <section id="testimonials" className="testimonials">
      <div className="container">
        <div className="testimonials-header">
          <h2 className="testimonials-title">What Our Clients Say</h2>
          <p className="testimonials-subtitle">
            Don't just take our word for it. Here's what our satisfied customers have to say about our services.
          </p>
        </div>

        <div className="testimonials-gallery">
          <div
            className="testimonials-container"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div
              className="slides-wrapper"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="testimonial-slide">
                  <div className="testimonial-card">
                    <div className="quote-icon">
                      <Quote size={32} />
                    </div>

                    <div className="rating">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} size={20} fill="currentColor" />
                      ))}
                    </div>

                    <p className="testimonial-text">"{testimonial.text}"</p>

                    <div className="testimonial-author">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="author-image"
                      />
                      <div className="author-info">
                        <div className="author-name">{testimonial.name}</div>
                        <div className="author-company">{testimonial.company}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <button className="slider-btn prev-btn" onClick={prevSlide}>
              <ChevronLeft size={24} />
            </button>
            <button className="slider-btn next-btn" onClick={nextSlide}>
              <ChevronRight size={24} />
            </button>

            <div className="slider-dots">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={`dot ${index === currentIndex ? 'active' : ''}`}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .testimonials {
          padding: 5rem 0;
          background: linear-gradient(135deg, rgba(249, 115, 22, 0.03) 0%, var(--white) 35%, rgba(37, 99, 235, 0.02) 100%);
        }

        .testimonials-header {
          text-align: center;
          margin-bottom: 4rem;
        }

        .testimonials-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .testimonials-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        .testimonials-gallery {
          margin-bottom: 2rem;
        }

        .testimonials-container {
          position: relative;
          max-width: 1000px;
          margin: 0 auto;
          border-radius: 2rem;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
          background: linear-gradient(135deg, var(--white) 0%, rgba(37, 99, 235, 0.02) 50%, rgba(249, 115, 22, 0.02) 100%);
          min-height: 400px;
        }

        .slides-wrapper {
          display: flex;
          transition: transform 0.5s ease-in-out;
          border-radius: 2rem;
          width: 100%;
        }

        .testimonial-slide {
          min-width: 100%;
          width: 100%;
          flex-shrink: 0;
          padding: 0;
        }

        .testimonial-card {
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          padding: 5rem 3rem 3rem;
          text-align: center;
          position: relative;
          height: 400px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .quote-icon {
          position: absolute;
          top: 1.5rem;
          left: 50%;
          transform: translateX(-50%);
          background: var(--gradient-primary);
          color: var(--white);
          width: 4rem;
          height: 4rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .rating {
          display: flex;
          justify-content: center;
          gap: 0.25rem;
          color: #fbbf24;
          margin-bottom: 1.5rem;
          margin-top: 1rem;
        }

        .testimonial-text {
          font-size: 1.125rem;
          line-height: 1.7;
          color: var(--gray-700);
          margin-bottom: 2rem;
          font-style: italic;
        }

        .testimonial-author {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .author-image {
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid var(--white);
          box-shadow: var(--shadow-md);
        }

        .author-info {
          text-align: left;
        }

        .author-name {
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 0.25rem;
        }

        .author-company {
          font-size: 0.875rem;
          color: var(--gray-600);
        }

        .slider-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(15px);
          border: 2px solid rgba(37, 99, 235, 0.1);
          border-radius: 50%;
          width: 4rem;
          height: 4rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--gray-700);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          z-index: 20;
        }

        .slider-btn:hover {
          background: var(--primary-blue);
          border-color: var(--primary-blue);
          color: var(--white);
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 12px 35px rgba(37, 99, 235, 0.3);
        }

        .prev-btn {
          left: 2rem;
        }

        .next-btn {
          right: 2rem;
        }

        .slider-dots {
          position: absolute;
          bottom: 1.5rem;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          justify-content: center;
          gap: 0.75rem;
        }

        .dot {
          width: 0.75rem;
          height: 0.75rem;
          border-radius: 50%;
          border: none;
          background: var(--gray-300);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .dot.active {
          background: var(--primary-blue);
          transform: scale(1.2);
        }

        .dot:hover {
          background: var(--primary-blue);
        }

        @media (max-width: 768px) {
          .testimonials-title {
            font-size: 2rem;
          }

          .testimonial-card {
            padding: 2rem 1.5rem;
          }

          .testimonial-text {
            font-size: 1rem;
          }

          .slider-btn {
            display: none;
          }

          .author-info {
            text-align: center;
          }

          .testimonial-author {
            flex-direction: column;
            gap: 0.5rem;
          }
        }
      `}</style>
    </section>
  );
};

export default Testimonials;
