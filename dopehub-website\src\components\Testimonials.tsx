import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      company: 'Creative Studio',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'DopeHub exceeded our expectations! The DTF prints were vibrant and durable. Our clients loved the custom t-shirts we created for their event.'
    },
    {
      id: 2,
      name: '<PERSON>',
      company: 'Local Sports Team',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Professional embroidery work on our team jerseys. The quality is outstanding and the turnaround time was incredibly fast. Highly recommended!'
    },
    {
      id: 3,
      name: '<PERSON>',
      company: 'Marketing Agency',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'Amazing vinyl work for our client\'s storefront. The graphics look professional and have held up perfectly through all weather conditions.'
    },
    {
      id: 4,
      name: 'David Thompson',
      company: 'Coffee Shop Owner',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'The sublimation mugs turned out perfect! Great quality and the colors are exactly what we wanted. Our customers love them.'
    },
    {
      id: 5,
      name: 'Lisa Park',
      company: 'Event Coordinator',
      image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      text: 'DopeHub handled our large order with ease. Screen printing for 500+ shirts was completed on time and with consistent quality throughout.'
    }
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentIndex, isAutoPlaying]);

  const handleMouseEnter = () => setIsAutoPlaying(false);
  const handleMouseLeave = () => setIsAutoPlaying(true);

  return (
    <section id="testimonials" className="testimonials">
      <div className="container">
        <div className="testimonials-header">
          <h2 className="testimonials-title">What Our Clients Say</h2>
          <p className="testimonials-subtitle">
            Don't just take our word for it. Here's what our satisfied customers have to say about our services.
          </p>
        </div>

        <div 
          className="testimonials-slider"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="slider-container">
            <div 
              className="slides-wrapper"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="testimonial-slide">
                  <div className="testimonial-card">
                    <div className="quote-icon">
                      <Quote size={32} />
                    </div>
                    
                    <div className="rating">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} size={20} fill="currentColor" />
                      ))}
                    </div>
                    
                    <p className="testimonial-text">"{testimonial.text}"</p>
                    
                    <div className="testimonial-author">
                      <img 
                        src={testimonial.image} 
                        alt={testimonial.name}
                        className="author-image"
                      />
                      <div className="author-info">
                        <div className="author-name">{testimonial.name}</div>
                        <div className="author-company">{testimonial.company}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <button className="slider-btn prev-btn" onClick={prevSlide}>
            <ChevronLeft size={24} />
          </button>
          <button className="slider-btn next-btn" onClick={nextSlide}>
            <ChevronRight size={24} />
          </button>

          <div className="slider-dots">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`dot ${index === currentIndex ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              />
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        .testimonials {
          padding: 5rem 0;
          background: var(--white);
        }

        .testimonials-header {
          text-align: center;
          margin-bottom: 4rem;
        }

        .testimonials-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--gray-900);
          margin-bottom: 1rem;
        }

        .testimonials-subtitle {
          font-size: 1.125rem;
          color: var(--gray-600);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.7;
        }

        .testimonials-slider {
          position: relative;
          max-width: 800px;
          margin: 0 auto;
        }

        .slider-container {
          overflow: hidden;
          border-radius: 1rem;
        }

        .slides-wrapper {
          display: flex;
          transition: transform 0.5s ease-in-out;
        }

        .testimonial-slide {
          min-width: 100%;
          padding: 0 1rem;
        }

        .testimonial-card {
          background: var(--gray-50);
          padding: 3rem 2rem;
          border-radius: 1rem;
          text-align: center;
          position: relative;
          box-shadow: var(--shadow-md);
        }

        .quote-icon {
          position: absolute;
          top: -16px;
          left: 50%;
          transform: translateX(-50%);
          background: var(--gradient-primary);
          color: var(--white);
          width: 4rem;
          height: 4rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .rating {
          display: flex;
          justify-content: center;
          gap: 0.25rem;
          color: #fbbf24;
          margin-bottom: 1.5rem;
          margin-top: 1rem;
        }

        .testimonial-text {
          font-size: 1.125rem;
          line-height: 1.7;
          color: var(--gray-700);
          margin-bottom: 2rem;
          font-style: italic;
        }

        .testimonial-author {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .author-image {
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid var(--white);
          box-shadow: var(--shadow-md);
        }

        .author-info {
          text-align: left;
        }

        .author-name {
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 0.25rem;
        }

        .author-company {
          font-size: 0.875rem;
          color: var(--gray-600);
        }

        .slider-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: var(--white);
          border: 2px solid var(--gray-300);
          border-radius: 50%;
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--gray-600);
          box-shadow: var(--shadow-md);
        }

        .slider-btn:hover {
          background: var(--primary-blue);
          border-color: var(--primary-blue);
          color: var(--white);
        }

        .prev-btn {
          left: -1.5rem;
        }

        .next-btn {
          right: -1.5rem;
        }

        .slider-dots {
          display: flex;
          justify-content: center;
          gap: 0.5rem;
          margin-top: 2rem;
        }

        .dot {
          width: 0.75rem;
          height: 0.75rem;
          border-radius: 50%;
          border: none;
          background: var(--gray-300);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .dot.active {
          background: var(--primary-blue);
          transform: scale(1.2);
        }

        .dot:hover {
          background: var(--primary-blue);
        }

        @media (max-width: 768px) {
          .testimonials-title {
            font-size: 2rem;
          }

          .testimonial-card {
            padding: 2rem 1.5rem;
          }

          .testimonial-text {
            font-size: 1rem;
          }

          .slider-btn {
            display: none;
          }

          .author-info {
            text-align: center;
          }

          .testimonial-author {
            flex-direction: column;
            gap: 0.5rem;
          }
        }
      `}</style>
    </section>
  );
};

export default Testimonials;
